// 配置工具函数
// 统一管理应用配置，支持开发和生产环境

/**
 * 获取下载器基础URL
 * @returns 下载器基础URL
 */
export function getDownloaderBaseUrl(): string {
  // 从环境变量获取配置，如果未定义则使用默认值
  const baseUrl = process.env.PLASMO_PUBLIC_DOWNLOADER_URL || 'http://localhost:3000'
  return baseUrl
}

/**
 * 获取完整的下载器URL
 * @param requestId 请求ID
 * @returns 完整的下载器URL
 */
export function getDownloaderUrl(requestId: string): string {
  const baseUrl = getDownloaderBaseUrl()
  return `${baseUrl}/downloader?requestId=${requestId}`
}

/**
 * 获取下载器页面的匹配模式（用于content script）
 * @returns 匹配模式数组
 */
export function getDownloaderMatches(): string[] {
  // 包含所有可能的下载器URL模式
  return [
    "http://localhost:3000/*",
    "http://localhost:3456/*"
  ]
}

/**
 * 检查当前是否为开发环境
 * @returns 是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查当前是否为生产环境
 * @returns 是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}
